# 中国中铁量化交易系统

这是一个专门针对中国中铁(601390)股票的量化交易系统，基于Python开发，支持技术指标分析、交易策略回测和风险管理。

## 功能特性

- 📈 **股票数据获取**: 支持从多个数据源获取中国中铁的历史和实时数据
- 🔧 **技术指标计算**: 内置常用技术指标（MA、MACD、RSI、布林带等）
- 🎯 **交易策略**: 实现移动平均线策略，支持自定义策略开发
- ⚖️ **风险管理**: 完善的止损止盈和仓位管理功能
- 📊 **回测系统**: 历史数据回测，验证策略有效性
- 📝 **日志监控**: 完整的交易日志记录和监控功能

## 项目结构

```
量化交易/
├── src/                    # 源代码目录
│   ├── data/              # 数据获取模块
│   ├── indicators/        # 技术指标模块
│   ├── strategies/        # 交易策略模块
│   ├── risk/             # 风险管理模块
│   ├── backtest/         # 回测模块
│   ├── trading/          # 交易执行模块
│   └── utils/            # 工具模块
├── logs/                  # 日志文件目录
├── config.py             # 配置文件
├── main.py              # 主程序
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 安装和使用

### 1. 环境要求

- Python 3.8+
- Windows/Linux/macOS

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置设置

复制 `.env.example` 为 `.env` 并根据需要修改配置：

```bash
cp .env.example .env
```

在 `config.py` 中可以调整以下参数：
- `INITIAL_CAPITAL`: 初始资金
- `MAX_POSITION_RATIO`: 最大仓位比例
- `STOP_LOSS_RATIO`: 止损比例
- `TAKE_PROFIT_RATIO`: 止盈比例
- `MA_SHORT_PERIOD`: 短期均线周期
- `MA_LONG_PERIOD`: 长期均线周期

### 4. 运行程序

```bash
python main.py
```

## 主要模块说明

### 数据获取模块 (`src/data/`)

- 支持从AKShare获取股票数据
- 提供历史数据和实时行情接口
- 内置数据缓存机制

### 技术指标模块 (`src/indicators/`)

实现的技术指标包括：
- 简单移动平均线 (SMA)
- 指数移动平均线 (EMA)
- 相对强弱指数 (RSI)
- MACD指标
- 布林带 (Bollinger Bands)
- 随机指标 (Stochastic)
- 威廉指标 (Williams %R)
- 平均真实波幅 (ATR)
- 能量潮指标 (OBV)

### 交易策略模块 (`src/strategies/`)

当前实现的策略：
- **移动平均线策略**: 基于短期和长期均线的金叉死叉进行交易

策略特点：
- 支持自定义参数
- 内置信号置信度计算
- 可扩展的策略框架

### 风险管理模块 (`src/risk/`)

风险控制功能：
- 动态仓位管理
- 止损止盈设置
- 风险指标计算
- 投资组合监控

### 回测系统 (`src/backtest/`)

回测功能：
- 历史数据回测
- 多种性能指标计算
- 可视化结果展示
- 策略比较分析

## 使用示例

### 基本使用

```python
from src.data.stock_data import get_china_railway_data
from src.indicators.technical_indicators import calculate_all_indicators
from src.strategies.moving_average_strategy import MovingAverageStrategy
from src.backtest.backtest_engine import BacktestEngine

# 获取数据
data = get_china_railway_data("2023-01-01", "2024-01-01")

# 计算技术指标
data_with_indicators = calculate_all_indicators(data)

# 创建策略
strategy = MovingAverageStrategy(short_period=5, long_period=20)

# 运行回测
engine = BacktestEngine()
result = engine.run_backtest(strategy, data_with_indicators)

# 查看结果
print(f"总收益率: {result.total_return_pct:.2f}%")
```

### 自定义策略

```python
from src.strategies.base_strategy import BaseStrategy, TradingSignal, SignalType

class MyStrategy(BaseStrategy):
    def initialize(self, data):
        # 策略初始化逻辑
        pass
    
    def generate_signal(self, data, current_index):
        # 信号生成逻辑
        return TradingSignal(
            signal_type=SignalType.BUY,
            price=data.iloc[current_index]['close'],
            timestamp=data.index[current_index],
            confidence=0.8,
            reason="自定义信号"
        )
```

## 注意事项

⚠️ **重要提醒**:
1. 本系统仅供学习和研究使用
2. 默认为模拟交易模式，实际交易需要接入券商API
3. 投资有风险，使用本系统进行实际交易请谨慎
4. 建议在充分回测和验证后再考虑实盘交易

## 风险声明

- 本系统不构成投资建议
- 历史表现不代表未来收益
- 请根据自身风险承受能力谨慎投资
- 作者不对使用本系统造成的任何损失负责

## 技术支持

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
