"""
配置文件
"""
import os
from dotenv import load_dotenv

load_dotenv()

# 股票配置
STOCK_CODE = "601390"  # 中国中铁
STOCK_NAME = "中国中铁"

# 交易配置
INITIAL_CAPITAL = 100000  # 初始资金
MAX_POSITION_RATIO = 0.8  # 最大仓位比例
STOP_LOSS_RATIO = 0.05  # 止损比例 5%
TAKE_PROFIT_RATIO = 0.10  # 止盈比例 10%

# 技术指标参数
MA_SHORT_PERIOD = 5  # 短期均线
MA_LONG_PERIOD = 20  # 长期均线
RSI_PERIOD = 14  # RSI周期
MACD_FAST = 12  # MACD快线
MACD_SLOW = 26  # MACD慢线
MACD_SIGNAL = 9  # MACD信号线

# 数据配置
DATA_START_DATE = "2020-01-01"
DATA_END_DATE = None  # None表示当前日期

# 日志配置
LOG_LEVEL = "INFO"
LOG_FILE = "trading.log"

# 东方财富配置（模拟交易）
EASTMONEY_ACCOUNT = os.getenv("EASTMONEY_ACCOUNT", "")
EASTMONEY_PASSWORD = os.getenv("EASTMONEY_PASSWORD", "")
SIMULATION_MODE = True  # 模拟交易模式
