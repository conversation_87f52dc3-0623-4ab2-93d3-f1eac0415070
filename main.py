"""
中国中铁量化交易主程序
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

from src.utils.logger import setup_logger
from src.data.stock_data import get_china_railway_data, get_china_railway_realtime
from src.indicators.technical_indicators import calculate_all_indicators
from src.strategies.moving_average_strategy import MovingAverageStrategy
from src.risk.risk_manager import RiskManager
from src.backtest.backtest_engine import BacktestEngine
from config import *


def main():
    """主函数"""
    # 初始化日志
    setup_logger(LOG_LEVEL, LOG_FILE)
    
    print("=" * 60)
    print("中国中铁(601390)量化交易系统")
    print("=" * 60)
    
    # 获取历史数据
    print("\n1. 获取历史数据...")
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=365*2)).strftime('%Y-%m-%d')
    
    stock_data = get_china_railway_data(start_date, end_date)
    
    if stock_data.empty:
        print("❌ 获取股票数据失败，请检查网络连接")
        return
    
    print(f"✅ 成功获取数据，共 {len(stock_data)} 条记录")
    print(f"📅 数据范围: {stock_data.index[0].strftime('%Y-%m-%d')} 至 {stock_data.index[-1].strftime('%Y-%m-%d')}")
    
    # 计算技术指标
    print("\n2. 计算技术指标...")
    stock_data_with_indicators = calculate_all_indicators(stock_data)
    print("✅ 技术指标计算完成")
    
    # 显示最新数据
    print("\n3. 最新数据预览:")
    latest_data = stock_data_with_indicators.tail(1)
    print(f"日期: {latest_data.index[0].strftime('%Y-%m-%d')}")
    print(f"收盘价: {latest_data['close'].iloc[0]:.2f}")
    print(f"5日均线: {latest_data['sma_5'].iloc[0]:.2f}")
    print(f"20日均线: {latest_data['sma_20'].iloc[0]:.2f}")
    print(f"RSI: {latest_data['rsi'].iloc[0]:.2f}")
    
    # 创建交易策略
    print("\n4. 初始化交易策略...")
    strategy = MovingAverageStrategy(
        short_period=MA_SHORT_PERIOD,
        long_period=MA_LONG_PERIOD,
        ma_type="sma"
    )
    print(f"✅ 策略创建完成: {strategy.name}")
    
    # 创建风险管理器
    risk_manager = RiskManager(
        initial_capital=INITIAL_CAPITAL,
        max_position_ratio=MAX_POSITION_RATIO,
        stop_loss_ratio=STOP_LOSS_RATIO,
        take_profit_ratio=TAKE_PROFIT_RATIO
    )
    print(f"✅ 风险管理器创建完成，初始资金: {INITIAL_CAPITAL:,.0f} 元")
    
    # 运行回测
    print("\n5. 运行历史回测...")
    backtest_engine = BacktestEngine(initial_capital=INITIAL_CAPITAL)
    
    # 使用最近一年的数据进行回测
    backtest_start = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    
    try:
        result = backtest_engine.run_backtest(
            strategy=strategy,
            data=stock_data_with_indicators,
            start_date=backtest_start,
            end_date=end_date,
            risk_manager=risk_manager
        )
        
        print("✅ 回测完成")
        print("\n📊 回测结果:")
        print(f"总收益率: {result.total_return_pct:.2f}%")
        print(f"基准收益率: {result.benchmark_return:.2f}%")
        print(f"最大回撤: {result.max_drawdown:.2f}%")
        print(f"夏普比率: {result.sharpe_ratio:.2f}")
        print(f"胜率: {result.win_rate:.1f}%")
        print(f"盈亏比: {result.profit_factor:.2f}")
        print(f"总交易次数: {result.total_trades}")
        
        # 绘制回测结果
        print("\n6. 生成回测图表...")
        backtest_engine.plot_results(result, "logs/backtest_result.png")
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 获取实时行情
    print("\n7. 获取实时行情...")
    try:
        realtime_data = get_china_railway_realtime()
        if realtime_data:
            print("✅ 实时行情:")
            print(f"当前价格: {realtime_data['current_price']:.2f}")
            print(f"涨跌幅: {realtime_data['change_pct']:.2f}%")
            print(f"成交量: {realtime_data['volume']:,}")
            print(f"更新时间: {realtime_data['timestamp']}")
        else:
            print("⚠️ 无法获取实时行情（可能是非交易时间）")
    except Exception as e:
        print(f"⚠️ 获取实时行情失败: {e}")
    
    # 生成当前交易信号
    print("\n8. 生成交易信号...")
    try:
        # 使用最新数据生成信号
        current_index = len(stock_data_with_indicators) - 1
        signal = strategy.generate_signal(stock_data_with_indicators, current_index)
        
        print(f"📈 当前交易信号: {signal.signal_type.value}")
        print(f"信号价格: {signal.price:.2f}")
        print(f"置信度: {signal.confidence:.2f}")
        print(f"信号原因: {signal.reason}")
        
        if signal.signal_type.value != "HOLD":
            print(f"⚠️ 注意: 当前为模拟交易模式，实际交易请谨慎操作！")
        
    except Exception as e:
        print(f"❌ 生成交易信号失败: {e}")
    
    print("\n" + "=" * 60)
    print("量化交易系统运行完成")
    print("=" * 60)


def run_live_trading():
    """实时交易模式（模拟）"""
    print("🔄 启动实时交易监控...")
    print("⚠️ 当前为模拟交易模式")
    
    # 这里可以添加实时交易逻辑
    # 例如：定时获取数据、生成信号、执行交易等
    pass


if __name__ == "__main__":
    try:
        main()
        
        # 询问是否启动实时交易
        if SIMULATION_MODE:
            choice = input("\n是否启动实时交易监控？(y/n): ").lower()
            if choice == 'y':
                run_live_trading()
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
