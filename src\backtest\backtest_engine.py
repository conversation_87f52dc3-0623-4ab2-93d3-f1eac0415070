"""
回测引擎
用于验证交易策略的历史表现
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from loguru import logger

from ..strategies.base_strategy import BaseStrategy, TradingSignal, SignalType
from ..risk.risk_manager import RiskManager, Position
from ..indicators.technical_indicators import calculate_all_indicators


@dataclass
class BacktestResult:
    """回测结果"""
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_pct: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    trade_history: List[Dict]
    equity_curve: pd.Series
    benchmark_return: float = 0.0


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 100000, commission: float = 0.0003):
        """
        初始化回测引擎
        
        Args:
            initial_capital: 初始资金
            commission: 手续费率
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.results: List[BacktestResult] = []
        
    def run_backtest(self, strategy: BaseStrategy, data: pd.DataFrame, 
                    start_date: str = None, end_date: str = None,
                    risk_manager: RiskManager = None) -> BacktestResult:
        """
        运行回测
        
        Args:
            strategy: 交易策略
            data: 历史数据
            start_date: 开始日期
            end_date: 结束日期
            risk_manager: 风险管理器
        
        Returns:
            回测结果
        """
        logger.info(f"开始回测策略: {strategy.name}")
        
        # 数据预处理
        backtest_data = self._prepare_data(data, start_date, end_date)
        if backtest_data.empty:
            raise ValueError("回测数据为空")
        
        # 计算技术指标
        backtest_data = calculate_all_indicators(backtest_data)
        
        # 初始化策略
        strategy.clear_signals()
        strategy.initialize(backtest_data)
        
        # 初始化风险管理器
        if risk_manager is None:
            risk_manager = RiskManager(initial_capital=self.initial_capital)
        
        # 回测变量
        equity_curve = []
        trade_signals = []
        current_position = None
        
        # 逐日回测
        for i in range(len(backtest_data)):
            current_date = backtest_data.index[i]
            current_price = backtest_data.iloc[i]['close']
            
            # 更新持仓价值
            market_data = {'stock': current_price}
            risk_manager.update_positions(market_data)
            
            # 检查止损止盈
            to_close = risk_manager.check_stop_loss_take_profit(market_data)
            for symbol, reason in to_close.items():
                risk_manager.close_position(symbol, current_price, reason)
                current_position = None
            
            # 生成交易信号
            signal = strategy.generate_signal(backtest_data, i)
            trade_signals.append({
                'date': current_date,
                'signal': signal.signal_type.value,
                'price': current_price,
                'confidence': signal.confidence,
                'reason': signal.reason
            })
            
            # 执行交易
            if signal.signal_type == SignalType.BUY and current_position is None:
                # 买入信号且无持仓
                quantity = risk_manager.calculate_position_size('stock', current_price, signal.confidence)
                if quantity > 0:
                    # 计算手续费
                    commission_cost = quantity * current_price * self.commission
                    if risk_manager.cash >= quantity * current_price + commission_cost:
                        risk_manager.open_position('stock', quantity, current_price)
                        risk_manager.cash -= commission_cost  # 扣除手续费
                        current_position = 'long'
                        
            elif signal.signal_type == SignalType.SELL and current_position == 'long':
                # 卖出信号且有持仓
                if 'stock' in risk_manager.positions:
                    position = risk_manager.positions['stock']
                    commission_cost = position.quantity * current_price * self.commission
                    risk_manager.close_position('stock', current_price, "策略信号")
                    risk_manager.cash -= commission_cost  # 扣除手续费
                    current_position = None
            
            # 记录权益曲线
            portfolio_value = risk_manager.calculate_portfolio_value()
            equity_curve.append(portfolio_value)
        
        # 如果回测结束时还有持仓，强制平仓
        if 'stock' in risk_manager.positions:
            final_price = backtest_data.iloc[-1]['close']
            risk_manager.close_position('stock', final_price, "回测结束")
        
        # 计算回测结果
        result = self._calculate_results(
            strategy, backtest_data, risk_manager, equity_curve, trade_signals
        )
        
        self.results.append(result)
        logger.info(f"回测完成: {strategy.name}, 总收益率: {result.total_return_pct:.2f}%")
        
        return result
    
    def _prepare_data(self, data: pd.DataFrame, start_date: str = None, 
                     end_date: str = None) -> pd.DataFrame:
        """准备回测数据"""
        df = data.copy()
        
        # 确保索引是日期类型
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
            else:
                raise ValueError("数据必须包含日期索引或日期列")
        
        # 过滤日期范围
        if start_date:
            df = df[df.index >= start_date]
        if end_date:
            df = df[df.index <= end_date]
        
        # 排序
        df = df.sort_index()
        
        return df
    
    def _calculate_results(self, strategy: BaseStrategy, data: pd.DataFrame,
                          risk_manager: RiskManager, equity_curve: List[float],
                          trade_signals: List[Dict]) -> BacktestResult:
        """计算回测结果"""
        
        # 基本统计
        final_capital = equity_curve[-1]
        total_return = final_capital - self.initial_capital
        total_return_pct = total_return / self.initial_capital * 100
        
        # 权益曲线
        equity_series = pd.Series(equity_curve, index=data.index)
        
        # 计算最大回撤
        running_max = equity_series.expanding().max()
        drawdown = (equity_series - running_max) / running_max
        max_drawdown = drawdown.min() * 100
        
        # 计算夏普比率
        returns = equity_series.pct_change().dropna()
        if returns.std() > 0:
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252)
        else:
            sharpe_ratio = 0
        
        # 交易统计
        trades = [t for t in risk_manager.trade_history if t['action'] == 'SELL']
        total_trades = len(trades)
        
        if total_trades > 0:
            winning_trades = len([t for t in trades if t['pnl'] > 0])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades * 100
            
            # 平均盈亏
            winning_pnl = [t['pnl'] for t in trades if t['pnl'] > 0]
            losing_pnl = [t['pnl'] for t in trades if t['pnl'] < 0]
            
            avg_win = np.mean(winning_pnl) if winning_pnl else 0
            avg_loss = np.mean(losing_pnl) if losing_pnl else 0
            
            # 盈亏比
            if avg_loss != 0:
                profit_factor = abs(avg_win / avg_loss)
            else:
                profit_factor = float('inf') if avg_win > 0 else 0
        else:
            winning_trades = losing_trades = 0
            win_rate = avg_win = avg_loss = profit_factor = 0
        
        # 基准收益（买入持有）
        benchmark_return = (data.iloc[-1]['close'] - data.iloc[0]['close']) / data.iloc[0]['close'] * 100
        
        return BacktestResult(
            strategy_name=strategy.name,
            start_date=data.index[0].strftime('%Y-%m-%d'),
            end_date=data.index[-1].strftime('%Y-%m-%d'),
            initial_capital=self.initial_capital,
            final_capital=final_capital,
            total_return=total_return,
            total_return_pct=total_return_pct,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            trade_history=risk_manager.trade_history,
            equity_curve=equity_series,
            benchmark_return=benchmark_return
        )
    
    def plot_results(self, result: BacktestResult, save_path: str = None) -> None:
        """绘制回测结果图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'回测结果: {result.strategy_name}', fontsize=16)
        
        # 权益曲线
        axes[0, 0].plot(result.equity_curve.index, result.equity_curve.values, 
                       label='策略收益', linewidth=2)
        axes[0, 0].axhline(y=result.initial_capital, color='r', linestyle='--', 
                          label='初始资金')
        axes[0, 0].set_title('权益曲线')
        axes[0, 0].set_ylabel('资金')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 回撤曲线
        running_max = result.equity_curve.expanding().max()
        drawdown = (result.equity_curve - running_max) / running_max * 100
        axes[0, 1].fill_between(drawdown.index, drawdown.values, 0, 
                               alpha=0.3, color='red')
        axes[0, 1].plot(drawdown.index, drawdown.values, color='red', linewidth=1)
        axes[0, 1].set_title('回撤曲线')
        axes[0, 1].set_ylabel('回撤 (%)')
        axes[0, 1].grid(True)
        
        # 月度收益热力图
        monthly_returns = result.equity_curve.resample('M').last().pct_change().dropna() * 100
        if len(monthly_returns) > 0:
            monthly_returns_pivot = monthly_returns.groupby([
                monthly_returns.index.year, monthly_returns.index.month
            ]).first().unstack()
            
            if not monthly_returns_pivot.empty:
                sns.heatmap(monthly_returns_pivot, annot=True, fmt='.1f', 
                           cmap='RdYlGn', center=0, ax=axes[1, 0])
                axes[1, 0].set_title('月度收益热力图 (%)')
        
        # 统计信息
        stats_text = f"""
        总收益率: {result.total_return_pct:.2f}%
        基准收益率: {result.benchmark_return:.2f}%
        最大回撤: {result.max_drawdown:.2f}%
        夏普比率: {result.sharpe_ratio:.2f}
        胜率: {result.win_rate:.1f}%
        盈亏比: {result.profit_factor:.2f}
        总交易次数: {result.total_trades}
        """
        
        axes[1, 1].text(0.1, 0.9, stats_text, transform=axes[1, 1].transAxes,
                        fontsize=12, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')
        axes[1, 1].set_title('统计信息')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"回测图表已保存: {save_path}")
        
        plt.show()
    
    def compare_strategies(self, results: List[BacktestResult]) -> pd.DataFrame:
        """比较多个策略的回测结果"""
        comparison_data = []
        
        for result in results:
            comparison_data.append({
                '策略名称': result.strategy_name,
                '总收益率(%)': result.total_return_pct,
                '最大回撤(%)': result.max_drawdown,
                '夏普比率': result.sharpe_ratio,
                '胜率(%)': result.win_rate,
                '盈亏比': result.profit_factor,
                '交易次数': result.total_trades,
                '基准收益率(%)': result.benchmark_return
            })
        
        return pd.DataFrame(comparison_data)
