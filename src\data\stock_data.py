"""
股票数据获取模块
支持从多个数据源获取股票数据
"""
import pandas as pd
import akshare as ak
import yfinance as yf
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from loguru import logger
import time
import requests


class StockDataProvider:
    """股票数据提供者基类"""
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取历史数据"""
        raise NotImplementedError
    
    def get_realtime_data(self, symbol: str) -> Dict[str, Any]:
        """获取实时数据"""
        raise NotImplementedError


class AkshareProvider(StockDataProvider):
    """AKShare数据提供者"""
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取历史K线数据
        
        Args:
            symbol: 股票代码，如 '601390'
            start_date: 开始日期，格式 'YYYY-MM-DD'
            end_date: 结束日期，格式 'YYYY-MM-DD'
        
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 使用akshare获取股票历史数据
            df = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date.replace("-", ""),
                end_date=end_date.replace("-", ""),
                adjust="qfq"  # 前复权
            )
            
            if df.empty:
                logger.warning(f"未获取到股票 {symbol} 的历史数据")
                return pd.DataFrame()
            
            # 重命名列名为标准格式
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover'
            })
            
            # 设置日期为索引
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            logger.info(f"成功获取股票 {symbol} 历史数据，共 {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_realtime_data(self, symbol: str) -> Dict[str, Any]:
        """
        获取实时行情数据
        
        Args:
            symbol: 股票代码，如 '601390'
        
        Returns:
            包含实时行情的字典
        """
        try:
            # 获取实时行情
            df = ak.stock_zh_a_spot_em()
            stock_data = df[df['代码'] == symbol]
            
            if stock_data.empty:
                logger.warning(f"未找到股票 {symbol} 的实时数据")
                return {}
            
            data = stock_data.iloc[0]
            
            realtime_info = {
                'symbol': symbol,
                'name': data.get('名称', ''),
                'current_price': float(data.get('最新价', 0)),
                'change': float(data.get('涨跌额', 0)),
                'change_pct': float(data.get('涨跌幅', 0)),
                'volume': int(data.get('成交量', 0)),
                'amount': float(data.get('成交额', 0)),
                'high': float(data.get('最高', 0)),
                'low': float(data.get('最低', 0)),
                'open': float(data.get('今开', 0)),
                'pre_close': float(data.get('昨收', 0)),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            logger.info(f"成功获取股票 {symbol} 实时数据")
            return realtime_info
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 实时数据失败: {e}")
            return {}


class StockDataManager:
    """股票数据管理器"""
    
    def __init__(self, provider: StockDataProvider = None):
        """
        初始化数据管理器
        
        Args:
            provider: 数据提供者，默认使用AkshareProvider
        """
        self.provider = provider or AkshareProvider()
        self.cache = {}  # 简单的内存缓存
        
    def get_stock_data(self, symbol: str, start_date: str = None, 
                      end_date: str = None, use_cache: bool = True) -> pd.DataFrame:
        """
        获取股票数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            use_cache: 是否使用缓存
        
        Returns:
            股票数据DataFrame
        """
        # 设置默认日期
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        cache_key = f"{symbol}_{start_date}_{end_date}"
        
        # 检查缓存
        if use_cache and cache_key in self.cache:
            logger.info(f"从缓存获取股票 {symbol} 数据")
            return self.cache[cache_key]
        
        # 获取数据
        data = self.provider.get_historical_data(symbol, start_date, end_date)
        
        # 缓存数据
        if use_cache and not data.empty:
            self.cache[cache_key] = data
        
        return data
    
    def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        """
        获取实时行情
        
        Args:
            symbol: 股票代码
        
        Returns:
            实时行情字典
        """
        return self.provider.get_realtime_data(symbol)
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        logger.info("缓存已清空")


# 创建全局数据管理器实例
data_manager = StockDataManager()


def get_china_railway_data(start_date: str = None, end_date: str = None) -> pd.DataFrame:
    """
    便捷函数：获取中国中铁股票数据
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        中国中铁股票数据
    """
    return data_manager.get_stock_data("601390", start_date, end_date)


def get_china_railway_realtime() -> Dict[str, Any]:
    """
    便捷函数：获取中国中铁实时行情
    
    Returns:
        中国中铁实时行情
    """
    return data_manager.get_realtime_quote("601390")
