"""
技术指标计算模块
实现常用的技术分析指标
"""
import pandas as pd
import numpy as np
from typing import Tuple, Optional
from loguru import logger


class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """
        简单移动平均线 (Simple Moving Average)
        
        Args:
            data: 价格序列
            period: 周期
        
        Returns:
            SMA序列
        """
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """
        指数移动平均线 (Exponential Moving Average)
        
        Args:
            data: 价格序列
            period: 周期
        
        Returns:
            EMA序列
        """
        return data.ewm(span=period).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """
        相对强弱指数 (Relative Strength Index)
        
        Args:
            data: 价格序列
            period: 周期，默认14
        
        Returns:
            RSI序列
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD指标 (Moving Average Convergence Divergence)
        
        Args:
            data: 价格序列
            fast: 快线周期，默认12
            slow: 慢线周期，默认26
            signal: 信号线周期，默认9
        
        Returns:
            (MACD线, 信号线, 柱状图)
        """
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        布林带 (Bollinger Bands)
        
        Args:
            data: 价格序列
            period: 周期，默认20
            std_dev: 标准差倍数，默认2
        
        Returns:
            (上轨, 中轨, 下轨)
        """
        middle_band = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        return upper_band, middle_band, lower_band
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                  k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        随机指标 (Stochastic Oscillator)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_period: K值周期，默认14
            d_period: D值周期，默认3
        
        Returns:
            (K值, D值)
        """
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return k_percent, d_percent
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        威廉指标 (Williams %R)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 周期，默认14
        
        Returns:
            Williams %R序列
        """
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        wr = -100 * ((highest_high - close) / (highest_high - lowest_low))
        
        return wr
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        平均真实波幅 (Average True Range)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 周期，默认14
        
        Returns:
            ATR序列
        """
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        能量潮指标 (On-Balance Volume)
        
        Args:
            close: 收盘价序列
            volume: 成交量序列
        
        Returns:
            OBV序列
        """
        price_change = close.diff()
        obv = pd.Series(index=close.index, dtype=float)
        
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if price_change.iloc[i] > 0:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif price_change.iloc[i] < 0:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv


def calculate_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算所有技术指标
    
    Args:
        df: 包含OHLCV数据的DataFrame
    
    Returns:
        包含所有技术指标的DataFrame
    """
    if df.empty:
        logger.warning("输入数据为空，无法计算技术指标")
        return df
    
    try:
        result_df = df.copy()
        
        # 移动平均线
        result_df['sma_5'] = TechnicalIndicators.sma(df['close'], 5)
        result_df['sma_10'] = TechnicalIndicators.sma(df['close'], 10)
        result_df['sma_20'] = TechnicalIndicators.sma(df['close'], 20)
        result_df['sma_60'] = TechnicalIndicators.sma(df['close'], 60)
        
        # 指数移动平均线
        result_df['ema_12'] = TechnicalIndicators.ema(df['close'], 12)
        result_df['ema_26'] = TechnicalIndicators.ema(df['close'], 26)
        
        # RSI
        result_df['rsi'] = TechnicalIndicators.rsi(df['close'])
        
        # MACD
        macd_line, signal_line, histogram = TechnicalIndicators.macd(df['close'])
        result_df['macd'] = macd_line
        result_df['macd_signal'] = signal_line
        result_df['macd_histogram'] = histogram
        
        # 布林带
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'])
        result_df['bb_upper'] = bb_upper
        result_df['bb_middle'] = bb_middle
        result_df['bb_lower'] = bb_lower
        
        # 随机指标
        if all(col in df.columns for col in ['high', 'low']):
            k_percent, d_percent = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'])
            result_df['stoch_k'] = k_percent
            result_df['stoch_d'] = d_percent
            
            # 威廉指标
            result_df['williams_r'] = TechnicalIndicators.williams_r(df['high'], df['low'], df['close'])
            
            # ATR
            result_df['atr'] = TechnicalIndicators.atr(df['high'], df['low'], df['close'])
        
        # OBV
        if 'volume' in df.columns:
            result_df['obv'] = TechnicalIndicators.obv(df['close'], df['volume'])
        
        logger.info("成功计算所有技术指标")
        return result_df
        
    except Exception as e:
        logger.error(f"计算技术指标失败: {e}")
        return df
