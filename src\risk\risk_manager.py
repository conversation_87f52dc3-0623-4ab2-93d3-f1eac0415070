"""
风险管理模块
实现止损止盈、仓位管理等风险控制功能
"""
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from loguru import logger
from enum import Enum

from ..strategies.base_strategy import TradingSignal, SignalType


class RiskLevel(Enum):
    """风险等级"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    EXTREME = "EXTREME"


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int
    entry_price: float
    entry_time: datetime
    current_price: float = 0.0
    stop_loss_price: float = 0.0
    take_profit_price: float = 0.0
    unrealized_pnl: float = 0.0
    unrealized_pnl_pct: float = 0.0
    
    def update_current_price(self, price: float) -> None:
        """更新当前价格和未实现盈亏"""
        self.current_price = price
        self.unrealized_pnl = (price - self.entry_price) * self.quantity
        self.unrealized_pnl_pct = (price - self.entry_price) / self.entry_price * 100


@dataclass
class RiskMetrics:
    """风险指标"""
    max_drawdown: float = 0.0
    current_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    var_95: float = 0.0  # 95% VaR
    risk_level: RiskLevel = RiskLevel.LOW


class RiskManager:
    """风险管理器"""
    
    def __init__(self, initial_capital: float = 100000, max_position_ratio: float = 0.8,
                 stop_loss_ratio: float = 0.05, take_profit_ratio: float = 0.10):
        """
        初始化风险管理器
        
        Args:
            initial_capital: 初始资金
            max_position_ratio: 最大仓位比例
            stop_loss_ratio: 止损比例
            take_profit_ratio: 止盈比例
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_position_ratio = max_position_ratio
        self.stop_loss_ratio = stop_loss_ratio
        self.take_profit_ratio = take_profit_ratio
        
        # 持仓管理
        self.positions: Dict[str, Position] = {}
        self.cash = initial_capital
        
        # 交易记录
        self.trade_history = []
        self.pnl_history = []
        
        # 风险指标
        self.risk_metrics = RiskMetrics()
        
    def calculate_position_size(self, symbol: str, price: float, 
                              signal_confidence: float = 1.0) -> int:
        """
        计算仓位大小
        
        Args:
            symbol: 股票代码
            price: 当前价格
            signal_confidence: 信号置信度
        
        Returns:
            建议仓位数量
        """
        try:
            # 可用资金
            available_cash = self.cash
            
            # 基础仓位比例（根据置信度调整）
            base_ratio = self.max_position_ratio * signal_confidence
            
            # 风险调整
            risk_adjustment = self._get_risk_adjustment()
            adjusted_ratio = base_ratio * risk_adjustment
            
            # 计算仓位金额
            position_value = available_cash * adjusted_ratio
            
            # 计算股票数量（向下取整到100的倍数）
            quantity = int(position_value / price / 100) * 100
            
            logger.info(f"计算仓位: {symbol}, 价格: {price}, 数量: {quantity}")
            return max(quantity, 0)
            
        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return 0
    
    def _get_risk_adjustment(self) -> float:
        """根据当前风险状况调整仓位"""
        if self.risk_metrics.risk_level == RiskLevel.LOW:
            return 1.0
        elif self.risk_metrics.risk_level == RiskLevel.MEDIUM:
            return 0.8
        elif self.risk_metrics.risk_level == RiskLevel.HIGH:
            return 0.5
        else:  # EXTREME
            return 0.2
    
    def open_position(self, symbol: str, quantity: int, price: float) -> bool:
        """
        开仓
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
        
        Returns:
            是否成功开仓
        """
        try:
            # 检查资金是否充足
            required_cash = quantity * price
            if required_cash > self.cash:
                logger.warning(f"资金不足，无法开仓: 需要 {required_cash}, 可用 {self.cash}")
                return False
            
            # 检查是否已有持仓
            if symbol in self.positions:
                logger.warning(f"已存在持仓: {symbol}")
                return False
            
            # 创建持仓
            position = Position(
                symbol=symbol,
                quantity=quantity,
                entry_price=price,
                entry_time=datetime.now(),
                current_price=price
            )
            
            # 设置止损止盈价格
            position.stop_loss_price = price * (1 - self.stop_loss_ratio)
            position.take_profit_price = price * (1 + self.take_profit_ratio)
            
            # 更新资金
            self.cash -= required_cash
            self.positions[symbol] = position
            
            # 记录交易
            self.trade_history.append({
                'action': 'BUY',
                'symbol': symbol,
                'quantity': quantity,
                'price': price,
                'timestamp': datetime.now(),
                'cash_after': self.cash
            })
            
            logger.info(f"开仓成功: {symbol}, 数量: {quantity}, 价格: {price}")
            return True
            
        except Exception as e:
            logger.error(f"开仓失败: {e}")
            return False
    
    def close_position(self, symbol: str, price: float, reason: str = "") -> bool:
        """
        平仓
        
        Args:
            symbol: 股票代码
            price: 平仓价格
            reason: 平仓原因
        
        Returns:
            是否成功平仓
        """
        try:
            if symbol not in self.positions:
                logger.warning(f"没有找到持仓: {symbol}")
                return False
            
            position = self.positions[symbol]
            
            # 计算盈亏
            pnl = (price - position.entry_price) * position.quantity
            pnl_pct = (price - position.entry_price) / position.entry_price * 100
            
            # 更新资金
            self.cash += position.quantity * price
            
            # 记录交易
            self.trade_history.append({
                'action': 'SELL',
                'symbol': symbol,
                'quantity': position.quantity,
                'price': price,
                'pnl': pnl,
                'pnl_pct': pnl_pct,
                'timestamp': datetime.now(),
                'reason': reason,
                'cash_after': self.cash
            })
            
            # 记录盈亏
            self.pnl_history.append(pnl)
            
            # 删除持仓
            del self.positions[symbol]
            
            logger.info(f"平仓成功: {symbol}, 价格: {price}, 盈亏: {pnl:.2f} ({pnl_pct:.2f}%), 原因: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"平仓失败: {e}")
            return False
    
    def update_positions(self, market_data: Dict[str, float]) -> None:
        """
        更新持仓信息
        
        Args:
            market_data: 市场数据 {symbol: current_price}
        """
        for symbol, position in self.positions.items():
            if symbol in market_data:
                position.update_current_price(market_data[symbol])
    
    def check_stop_loss_take_profit(self, market_data: Dict[str, float]) -> Dict[str, str]:
        """
        检查止损止盈
        
        Args:
            market_data: 市场数据
        
        Returns:
            需要平仓的股票及原因
        """
        to_close = {}
        
        for symbol, position in self.positions.items():
            if symbol in market_data:
                current_price = market_data[symbol]
                
                # 检查止损
                if current_price <= position.stop_loss_price:
                    to_close[symbol] = f"止损: {current_price} <= {position.stop_loss_price:.2f}"
                
                # 检查止盈
                elif current_price >= position.take_profit_price:
                    to_close[symbol] = f"止盈: {current_price} >= {position.take_profit_price:.2f}"
        
        return to_close
    
    def calculate_portfolio_value(self) -> float:
        """计算投资组合总价值"""
        portfolio_value = self.cash
        
        for position in self.positions.values():
            portfolio_value += position.quantity * position.current_price
        
        return portfolio_value
    
    def calculate_risk_metrics(self) -> RiskMetrics:
        """计算风险指标"""
        try:
            if len(self.pnl_history) < 2:
                return self.risk_metrics
            
            pnl_series = pd.Series(self.pnl_history)
            
            # 计算最大回撤
            cumulative_pnl = pnl_series.cumsum()
            running_max = cumulative_pnl.expanding().max()
            drawdown = (cumulative_pnl - running_max)
            self.risk_metrics.max_drawdown = drawdown.min()
            self.risk_metrics.current_drawdown = drawdown.iloc[-1]
            
            # 计算夏普比率
            if pnl_series.std() > 0:
                self.risk_metrics.sharpe_ratio = pnl_series.mean() / pnl_series.std() * np.sqrt(252)
            
            # 计算胜率
            winning_trades = (pnl_series > 0).sum()
            total_trades = len(pnl_series)
            self.risk_metrics.win_rate = winning_trades / total_trades * 100
            
            # 计算盈亏比
            winning_pnl = pnl_series[pnl_series > 0].sum()
            losing_pnl = abs(pnl_series[pnl_series < 0].sum())
            if losing_pnl > 0:
                self.risk_metrics.profit_factor = winning_pnl / losing_pnl
            
            # 计算VaR
            self.risk_metrics.var_95 = pnl_series.quantile(0.05)
            
            # 评估风险等级
            self.risk_metrics.risk_level = self._assess_risk_level()
            
            return self.risk_metrics
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return self.risk_metrics
    
    def _assess_risk_level(self) -> RiskLevel:
        """评估风险等级"""
        risk_score = 0
        
        # 回撤风险
        if self.risk_metrics.current_drawdown < -0.1:  # 回撤超过10%
            risk_score += 2
        elif self.risk_metrics.current_drawdown < -0.05:  # 回撤超过5%
            risk_score += 1
        
        # 胜率风险
        if self.risk_metrics.win_rate < 30:
            risk_score += 2
        elif self.risk_metrics.win_rate < 40:
            risk_score += 1
        
        # 盈亏比风险
        if self.risk_metrics.profit_factor < 0.8:
            risk_score += 2
        elif self.risk_metrics.profit_factor < 1.0:
            risk_score += 1
        
        # 根据风险评分确定等级
        if risk_score >= 5:
            return RiskLevel.EXTREME
        elif risk_score >= 3:
            return RiskLevel.HIGH
        elif risk_score >= 1:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        portfolio_value = self.calculate_portfolio_value()
        total_pnl = portfolio_value - self.initial_capital
        total_pnl_pct = total_pnl / self.initial_capital * 100
        
        return {
            'initial_capital': self.initial_capital,
            'current_capital': portfolio_value,
            'cash': self.cash,
            'total_pnl': total_pnl,
            'total_pnl_pct': total_pnl_pct,
            'positions_count': len(self.positions),
            'trades_count': len(self.trade_history),
            'risk_metrics': self.risk_metrics
        }
