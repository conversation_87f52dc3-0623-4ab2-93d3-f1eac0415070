"""
交易策略基类
定义策略接口和通用功能
"""
from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from loguru import logger


class SignalType(Enum):
    """信号类型枚举"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


@dataclass
class TradingSignal:
    """交易信号数据类"""
    signal_type: SignalType
    price: float
    timestamp: datetime
    confidence: float = 1.0  # 信号置信度 0-1
    reason: str = ""  # 信号原因
    metadata: Dict[str, Any] = None  # 额外信息
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseStrategy(ABC):
    """交易策略基类"""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        """
        初始化策略
        
        Args:
            name: 策略名称
            parameters: 策略参数
        """
        self.name = name
        self.parameters = parameters or {}
        self.signals_history: List[TradingSignal] = []
        self.is_initialized = False
        
    @abstractmethod
    def initialize(self, data: pd.DataFrame) -> None:
        """
        初始化策略
        
        Args:
            data: 历史数据
        """
        pass
    
    @abstractmethod
    def generate_signal(self, data: pd.DataFrame, current_index: int) -> TradingSignal:
        """
        生成交易信号
        
        Args:
            data: 包含技术指标的数据
            current_index: 当前数据索引
        
        Returns:
            交易信号
        """
        pass
    
    def add_signal(self, signal: TradingSignal) -> None:
        """添加信号到历史记录"""
        self.signals_history.append(signal)
        logger.info(f"策略 {self.name} 生成信号: {signal.signal_type.value} at {signal.price}")
    
    def get_latest_signal(self) -> Optional[TradingSignal]:
        """获取最新信号"""
        return self.signals_history[-1] if self.signals_history else None
    
    def get_signals_by_type(self, signal_type: SignalType) -> List[TradingSignal]:
        """根据类型获取信号"""
        return [s for s in self.signals_history if s.signal_type == signal_type]
    
    def clear_signals(self) -> None:
        """清空信号历史"""
        self.signals_history.clear()
    
    def get_parameter(self, key: str, default: Any = None) -> Any:
        """获取策略参数"""
        return self.parameters.get(key, default)
    
    def set_parameter(self, key: str, value: Any) -> None:
        """设置策略参数"""
        self.parameters[key] = value
    
    def validate_data(self, data: pd.DataFrame, required_columns: List[str]) -> bool:
        """
        验证数据完整性
        
        Args:
            data: 数据DataFrame
            required_columns: 必需的列名
        
        Returns:
            是否通过验证
        """
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            logger.error(f"策略 {self.name} 缺少必需的数据列: {missing_columns}")
            return False
        return True
    
    def __str__(self) -> str:
        return f"Strategy({self.name}, parameters={self.parameters})"
    
    def __repr__(self) -> str:
        return self.__str__()


class StrategyManager:
    """策略管理器"""
    
    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.active_strategy: Optional[BaseStrategy] = None
    
    def add_strategy(self, strategy: BaseStrategy) -> None:
        """添加策略"""
        self.strategies[strategy.name] = strategy
        logger.info(f"添加策略: {strategy.name}")
    
    def remove_strategy(self, name: str) -> None:
        """移除策略"""
        if name in self.strategies:
            del self.strategies[name]
            logger.info(f"移除策略: {name}")
            
            # 如果移除的是当前活跃策略，清空活跃策略
            if self.active_strategy and self.active_strategy.name == name:
                self.active_strategy = None
    
    def set_active_strategy(self, name: str) -> bool:
        """设置活跃策略"""
        if name in self.strategies:
            self.active_strategy = self.strategies[name]
            logger.info(f"设置活跃策略: {name}")
            return True
        else:
            logger.error(f"策略 {name} 不存在")
            return False
    
    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """获取策略"""
        return self.strategies.get(name)
    
    def list_strategies(self) -> List[str]:
        """列出所有策略名称"""
        return list(self.strategies.keys())
    
    def generate_signal(self, data: pd.DataFrame, current_index: int) -> Optional[TradingSignal]:
        """使用活跃策略生成信号"""
        if not self.active_strategy:
            logger.warning("没有设置活跃策略")
            return None
        
        if not self.active_strategy.is_initialized:
            logger.warning(f"策略 {self.active_strategy.name} 未初始化")
            return None
        
        try:
            signal = self.active_strategy.generate_signal(data, current_index)
            self.active_strategy.add_signal(signal)
            return signal
        except Exception as e:
            logger.error(f"策略 {self.active_strategy.name} 生成信号失败: {e}")
            return None


# 创建全局策略管理器实例
strategy_manager = StrategyManager()
