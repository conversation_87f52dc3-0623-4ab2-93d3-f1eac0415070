"""
移动平均线交易策略
基于短期和长期移动平均线的金叉死叉进行交易
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any
from loguru import logger

from .base_strategy import BaseStrategy, TradingSignal, SignalType


class MovingAverageStrategy(BaseStrategy):
    """移动平均线策略"""
    
    def __init__(self, short_period: int = 5, long_period: int = 20, 
                 ma_type: str = "sma", parameters: Dict[str, Any] = None):
        """
        初始化移动平均线策略
        
        Args:
            short_period: 短期均线周期
            long_period: 长期均线周期
            ma_type: 均线类型，'sma' 或 'ema'
            parameters: 其他参数
        """
        default_params = {
            'short_period': short_period,
            'long_period': long_period,
            'ma_type': ma_type,
            'min_confidence': 0.6  # 最小信号置信度
        }
        
        if parameters:
            default_params.update(parameters)
            
        super().__init__("MovingAverageStrategy", default_params)
        
        self.short_period = self.get_parameter('short_period')
        self.long_period = self.get_parameter('long_period')
        self.ma_type = self.get_parameter('ma_type')
        self.min_confidence = self.get_parameter('min_confidence')
        
        # 状态变量
        self.last_signal_type = SignalType.HOLD
        self.last_cross_index = -1
        
    def initialize(self, data: pd.DataFrame) -> None:
        """初始化策略"""
        required_columns = ['close']
        if not self.validate_data(data, required_columns):
            raise ValueError("数据验证失败")
        
        # 检查是否有足够的数据
        if len(data) < self.long_period:
            raise ValueError(f"数据长度不足，需要至少 {self.long_period} 条数据")
        
        # 检查是否已经计算了移动平均线
        short_ma_col = f"{self.ma_type}_{self.short_period}"
        long_ma_col = f"{self.ma_type}_{self.long_period}"
        
        if short_ma_col not in data.columns or long_ma_col not in data.columns:
            logger.warning(f"数据中缺少移动平均线列: {short_ma_col}, {long_ma_col}")
            logger.info("请确保在调用策略前已计算技术指标")
        
        self.is_initialized = True
        logger.info(f"移动平均线策略初始化完成: 短期={self.short_period}, 长期={self.long_period}")
    
    def generate_signal(self, data: pd.DataFrame, current_index: int) -> TradingSignal:
        """
        生成交易信号
        
        Args:
            data: 包含技术指标的数据
            current_index: 当前数据索引
        
        Returns:
            交易信号
        """
        if current_index < self.long_period:
            return TradingSignal(
                signal_type=SignalType.HOLD,
                price=data.iloc[current_index]['close'],
                timestamp=data.index[current_index],
                confidence=0.0,
                reason="数据不足"
            )
        
        # 获取移动平均线数据
        short_ma_col = f"{self.ma_type}_{self.short_period}"
        long_ma_col = f"{self.ma_type}_{self.long_period}"
        
        if short_ma_col not in data.columns or long_ma_col not in data.columns:
            return TradingSignal(
                signal_type=SignalType.HOLD,
                price=data.iloc[current_index]['close'],
                timestamp=data.index[current_index],
                confidence=0.0,
                reason="缺少移动平均线数据"
            )
        
        current_short_ma = data.iloc[current_index][short_ma_col]
        current_long_ma = data.iloc[current_index][long_ma_col]
        prev_short_ma = data.iloc[current_index - 1][short_ma_col]
        prev_long_ma = data.iloc[current_index - 1][long_ma_col]
        
        current_price = data.iloc[current_index]['close']
        timestamp = data.index[current_index]
        
        # 检查金叉和死叉
        signal_type = SignalType.HOLD
        confidence = 0.0
        reason = "无明确信号"
        
        # 金叉：短期均线从下方穿越长期均线
        if (prev_short_ma <= prev_long_ma and current_short_ma > current_long_ma):
            signal_type = SignalType.BUY
            confidence = self._calculate_confidence(data, current_index, "golden_cross")
            reason = f"金叉信号: 短期均线({self.short_period})上穿长期均线({self.long_period})"
            self.last_cross_index = current_index
            
        # 死叉：短期均线从上方穿越长期均线
        elif (prev_short_ma >= prev_long_ma and current_short_ma < current_long_ma):
            signal_type = SignalType.SELL
            confidence = self._calculate_confidence(data, current_index, "death_cross")
            reason = f"死叉信号: 短期均线({self.short_period})下穿长期均线({self.long_period})"
            self.last_cross_index = current_index
        
        # 记录最后信号类型
        if signal_type != SignalType.HOLD:
            self.last_signal_type = signal_type
        
        return TradingSignal(
            signal_type=signal_type,
            price=current_price,
            timestamp=timestamp,
            confidence=confidence,
            reason=reason,
            metadata={
                'short_ma': current_short_ma,
                'long_ma': current_long_ma,
                'ma_diff': current_short_ma - current_long_ma,
                'ma_diff_pct': (current_short_ma - current_long_ma) / current_long_ma * 100
            }
        )
    
    def _calculate_confidence(self, data: pd.DataFrame, current_index: int, cross_type: str) -> float:
        """
        计算信号置信度
        
        Args:
            data: 数据
            current_index: 当前索引
            cross_type: 交叉类型 'golden_cross' 或 'death_cross'
        
        Returns:
            置信度 (0-1)
        """
        try:
            # 基础置信度
            base_confidence = 0.7
            
            # 根据均线距离调整置信度
            short_ma_col = f"{self.ma_type}_{self.short_period}"
            long_ma_col = f"{self.ma_type}_{self.long_period}"
            
            current_short_ma = data.iloc[current_index][short_ma_col]
            current_long_ma = data.iloc[current_index][long_ma_col]
            
            # 均线距离越大，置信度越高
            ma_diff_pct = abs(current_short_ma - current_long_ma) / current_long_ma
            distance_bonus = min(ma_diff_pct * 10, 0.2)  # 最多增加0.2
            
            # 成交量确认（如果有成交量数据）
            volume_bonus = 0.0
            if 'volume' in data.columns and current_index >= 5:
                current_volume = data.iloc[current_index]['volume']
                avg_volume = data.iloc[current_index-5:current_index]['volume'].mean()
                
                if current_volume > avg_volume * 1.2:  # 成交量放大
                    volume_bonus = 0.1
            
            # 趋势确认
            trend_bonus = 0.0
            if current_index >= 10:
                recent_prices = data.iloc[current_index-10:current_index]['close']
                if cross_type == "golden_cross" and recent_prices.iloc[-1] > recent_prices.iloc[0]:
                    trend_bonus = 0.1
                elif cross_type == "death_cross" and recent_prices.iloc[-1] < recent_prices.iloc[0]:
                    trend_bonus = 0.1
            
            confidence = min(base_confidence + distance_bonus + volume_bonus + trend_bonus, 1.0)
            return confidence
            
        except Exception as e:
            logger.warning(f"计算置信度失败: {e}")
            return 0.6  # 返回默认置信度
