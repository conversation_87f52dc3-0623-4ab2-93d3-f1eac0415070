"""
简化版测试脚本
测试基本功能，不依赖复杂的第三方库
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_technical_indicators():
    """测试技术指标计算"""
    print("测试技术指标计算...")
    
    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    # 生成模拟股价数据
    base_price = 10.0
    returns = np.random.normal(0.001, 0.02, len(dates))
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 创建DataFrame
    data = pd.DataFrame({
        'date': dates,
        'close': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'volume': np.random.randint(1000000, 10000000, len(dates))
    })
    
    data.set_index('date', inplace=True)
    
    try:
        from indicators.technical_indicators import calculate_all_indicators
        
        # 计算技术指标
        data_with_indicators = calculate_all_indicators(data)
        
        print(f"✅ 成功计算技术指标，数据形状: {data_with_indicators.shape}")
        print(f"📊 指标列: {list(data_with_indicators.columns)}")
        
        # 显示最新数据
        latest = data_with_indicators.tail(1)
        print(f"\n最新数据 ({latest.index[0].strftime('%Y-%m-%d')}):")
        print(f"收盘价: {latest['close'].iloc[0]:.2f}")
        if 'sma_5' in latest.columns:
            print(f"5日均线: {latest['sma_5'].iloc[0]:.2f}")
        if 'sma_20' in latest.columns:
            print(f"20日均线: {latest['sma_20'].iloc[0]:.2f}")
        if 'rsi' in latest.columns:
            print(f"RSI: {latest['rsi'].iloc[0]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 技术指标计算失败: {e}")
        return False


def test_strategy():
    """测试交易策略"""
    print("\n测试交易策略...")
    
    try:
        from strategies.moving_average_strategy import MovingAverageStrategy
        from strategies.base_strategy import SignalType
        
        # 创建策略
        strategy = MovingAverageStrategy(short_period=5, long_period=20)
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        # 生成趋势数据（上涨趋势）
        prices = [10.0]
        for i in range(49):
            change = np.random.normal(0.01, 0.02)  # 轻微上涨趋势
            prices.append(prices[-1] * (1 + change))
        
        data = pd.DataFrame({
            'close': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'open': prices,
            'volume': [1000000] * 50
        }, index=dates)
        
        # 计算移动平均线
        data['sma_5'] = data['close'].rolling(5).mean()
        data['sma_20'] = data['close'].rolling(20).mean()
        
        # 初始化策略
        strategy.initialize(data)
        
        # 测试信号生成
        signals = []
        for i in range(25, len(data)):  # 从有足够数据的地方开始
            signal = strategy.generate_signal(data, i)
            if signal.signal_type != SignalType.HOLD:
                signals.append({
                    'date': data.index[i],
                    'type': signal.signal_type.value,
                    'price': signal.price,
                    'confidence': signal.confidence,
                    'reason': signal.reason
                })
        
        print(f"✅ 策略测试成功，生成 {len(signals)} 个交易信号")
        
        if signals:
            print("\n交易信号:")
            for signal in signals[:3]:  # 显示前3个信号
                print(f"  {signal['date'].strftime('%Y-%m-%d')}: {signal['type']} "
                      f"@ {signal['price']:.2f} (置信度: {signal['confidence']:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略测试失败: {e}")
        return False


def test_risk_manager():
    """测试风险管理"""
    print("\n测试风险管理...")
    
    try:
        from risk.risk_manager import RiskManager
        
        # 创建风险管理器
        risk_manager = RiskManager(initial_capital=100000)
        
        # 测试仓位计算
        position_size = risk_manager.calculate_position_size("601390", 10.0, 0.8)
        print(f"✅ 建议仓位: {position_size} 股")
        
        # 测试开仓
        success = risk_manager.open_position("601390", position_size, 10.0)
        if success:
            print(f"✅ 开仓成功")
            
            # 测试投资组合价值
            portfolio_value = risk_manager.calculate_portfolio_value()
            print(f"📊 投资组合价值: {portfolio_value:,.2f}")
            
            # 测试平仓
            success = risk_manager.close_position("601390", 10.5, "测试平仓")
            if success:
                print(f"✅ 平仓成功")
                
                # 显示交易历史
                if risk_manager.trade_history:
                    last_trade = risk_manager.trade_history[-1]
                    print(f"📈 最后交易盈亏: {last_trade.get('pnl', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险管理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("中国中铁量化交易系统 - 简化测试")
    print("=" * 60)
    
    # 测试各个模块
    tests = [
        ("技术指标模块", test_technical_indicators),
        ("交易策略模块", test_strategy),
        ("风险管理模块", test_risk_manager),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统基本功能正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")


if __name__ == "__main__":
    main()
